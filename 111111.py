from machine import *
from smartcar import *
from display import *
from seekfree import *
import gc
import os
import io


# 调用 IMU660RA 模块获取 IMU660RA 实例
# 参数是采集周期 调用多少次 capture 更新一次数据
# 可以不填 默认参数为 1 调整这个参数相当于调整采集分频
imu = IMU963RX()
#编码器
encoder_1 = encoder("D0", "D1",True)
encoder_2 = encoder("D2", "D3",False)
enc1_data = encoder_1.get()
enc2_data = encoder_2.get()
beep = Pin('D24', Pin.OUT, pull=Pin.PULL_UP_47K, value=0)  # 蜂鸣器
ccd = TSL1401(5)
# 屏幕初始化
cs = Pin('B29' , Pin.OUT, pull=Pin.PULL_UP_47K, value=1)
# 拉高拉低一次 CS 片选确保屏幕通信时序正常
cs.high()
cs.low()
# 定义控制引脚
rst = Pin('B9' , Pin.OUT, pull=Pin.PULL_UP_47K, value=1)
dc  = Pin('B8' , Pin.OUT, pull=Pin.PULL_UP_47K, value=1)
blk = Pin('C4' , Pin.OUT, pull=Pin.PULL_UP_47K, value=1)
# 新建 LCD 驱动实例 这里的索引范围与 SPI 示例一致 当前仅支持 IPS200
drv = LCD_Drv(SPI_INDEX=1, BAUDRATE=60000000, DC_PIN=dc, RST_PIN=rst, LCD_TYPE=LCD_Drv.LCD200_TYPE)
# 新建 LCD 实例
lcd = LCD(drv)
# color 接口设置屏幕显示颜色 [前景色,背景色]
lcd.color(0x0000, 0xFFFF)
# mode 接口设置屏幕显示模式 [0:竖屏,1:横屏,2:竖屏180旋转,3:横屏180旋转]
lcd.mode(0)
# 清屏
lcd.clear(0xFFFF)
def limit_min_max(value, min_value, max_value):
    return max(min(value, max_value), min_value)


# 定义一个回调函数 需要一个参数 这个参数就是 ticker 实例自身

class CCD_User:
    stop_flag = 0  # 停车标志位

    def __init__(self):
        # CCD参数 远端为0,近端为1
        self.gray_max = 100  # 灰度最大值
        self.gray_min = 15  # 灰度最小值
        self.CENTER = [58, 60]
        self.last_leftp = [31, 28]
        self.last_rightp = [84, 92]
        self.leftp = [31, 28]
        self.rightp = [84, 92]
        self.LEFTP = [34, 28]
        self.RIGHTP = [90, 98]
        self.LEFTB = [20, 12]
        self.RIGHTB = [107, 115]
        self.search = [15, 15]
        self.JUMPDOT = 3  # 跳点距离
        self.flag_l = [0, 0]  # 左搜线标志位
        self.flag_r = [0, 0]  # 右搜线标志位
        self.loss_flag = 0  # 近端丢线标志位
        self.roadwidth = [0, 0]  # 路宽
        self.ROADWIDTH = [53, 76]  # 路宽常量
        self.fit_loction1 = 0  # ccd1位置保存
        self.ccd_aver = [0, 0]  # 灰度平均值
        self.WBVALUE = [10, 10]  # 对比度阈值
        self.Loss_Count = 0  # 丢线计时
        # self.left_slope = [0, 0]  # 左斜率 0为上次的,1为这次的
        # self.right_slope = [0, 0]  # 右斜率 0为上次的,1为这次的
        self.CCD_location = [0, 0]  # 位置
        self.Fitting_location = 0  # 位置环拟合位置
        self.OLD_Fitting_location = 0  # 位置环上次拟合位置
        self.Fitting_speed_location = 0  # 速度环拟合位置
        # 起跑线
        self.point_count = 0  # 跳变点累计
        self.startline_flag = 0  # 起跑线标志位
        self.encoder_intergral = 0  # 起跑线编码器积分
        # 障碍
        self.brick_flag = 0  # 障碍判断
        self.brick_cnt = 0  # 障碍计时
        self.brick_direction = 0  # 障碍方向
        self.B_RIGHT = 1  # 右砖块标志位
        self.B_LEFT = 2  # 左砖块标志位
        self.brick_Speed = 80  # 障碍速度
        # 圆环
        self.annu3_right_point = 92  #98 34
        self.annu3_left_point = 28
        self.annu_num = 0  # 圆环数量
        self.annu_set_num = 0  # 圆环设定数量
        self.annu_flag = 0  # 圆环状态
        self.annu_dir = 0  # 圆环方向
        self.LEFT = 1  # 左圆环
        self.RIGHT = 2  # 右圆环
        self.now_leftp = 0
        self.now_rightp = 0
        self.keepmove1 = 0  # 状态1编码器累加值
        self.annu_angle_flag = 0  # 角度积分的标志位
        self.pre_rightp = 128  # 圆环右边界历史值
        self.pre_leftp = 0  # 圆环右边界历史值
        self.flag3_loss = 0  # 状态3退出标志位
        self.annu_angle = 0  # 角度积分
        self.flag4_loss = 0  # 状态4退出标志位
        self.keepmove4 = 0  # 结束前当前位置
        self.flag5_loss = 0  # 状态5退出标志位
        self.annu1_move = 700  # 圆环状态1进入
        self.annu3_lenth = 55  # 圆环状态3补线 #55
        self.annu5_lenth = 59  # 圆环状态5补线
        self.annu_cd = -100  # 圆环冷却
        #self.annu_Speed = 80  # 圆环速度
        # 坡道
        self.ramp_flag = 0  # 坡道状态
        self.ramp_move = 0
        self.ramp1_cnt = 0
        self.ramp_up_flag = 0
        self.ramp_up_angle = 0
        self.ramp_Speed = 80  # 坡道速度
        self.ramp_cd = -500  # 坡道冷却

    def ccd_handle(self, data, index):  # 灰度数据处理
        self.ccd_aver[index] = int(sum(data) // 128)

    def ccd_search(self, ccd_data, index):
        # 确定搜索区域
        # 向左搜
        if self.flag_l[index] == 0:
            left_start = self.rightp[index] - 8
            left_end = self.LEFTB[index]
        else:
            left_start = self.leftp[index] + self.search[index]  # 左起始点
            left_end = self.leftp[index] - self.search[index]  # 左截止点
            self.flag_l[index] = 0  # 左搜线标志位清零
        # 搜索区域限幅
        if left_start > self.RIGHTB[index]:  # 如果左起始点大于右边界
            left_start = self.RIGHTB[index]  # 将左起始点锁到右边界
        if left_end < self.LEFTB[index]:  # 如果左起始点大于右边界
            left_end = self.LEFTB[index]  # 将左起始点锁到右边界

        # 向右搜
        if self.flag_r[index] == 0:
            right_start = self.leftp[index] + 8
            right_end = self.RIGHTB[index]
        else:
            right_start = self.rightp[index] - self.search[index]  # 右起始点
            right_end = self.rightp[index] + self.search[index]  # 右截止点
            self.flag_r[index] = 0  # 右搜线标志位清零
        # 搜索区域限幅
        if right_start < self.LEFTB[index]:  # 如果右起始点小于左边界
            right_start = self.LEFTB[index]  # 将右起始点锁到右边界
        if right_end > self.RIGHTB[index]:  # 如果右截至点大于右边界
            right_end = self.RIGHTB[index]  # 将右截至点锁到右边界

        # 搜左拐点
        # 1搜到 0未搜到
        for i in range(left_start, left_end, -1):  # 从左起始点向左遍历
            endp = i - self.JUMPDOT  # 对比点(该点左边第'JUMPDOT'个点)
            if endp < self.LEFTB[index]:  # 越界锁定
                endp = self.LEFTB[index]
            # 如果该点灰度大于对比点,且该点与对比点的对比度大于阈值
            if ccd_data[i] > (ccd_data[endp] + 8):
                if ((ccd_data[i] - ccd_data[endp]) * 100 // (ccd_data[i] + ccd_data[endp])) > self.WBVALUE[index]:
                    self.last_leftp[index] = self.leftp[index]
                    self.leftp[index] = endp  # 该对比点为左边界
                    self.flag_l[index] = 1  # 则找到左边线
                    break  # 退出遍历

        # 搜右拐点
        # 1搜到 0未搜到
        for i in range(right_start, right_end, 1):  # 从右起始点向右遍历
            endp = i + self.JUMPDOT  # 对比点(该点右边第'JUMPDOT'个点)
            if endp > self.RIGHTB[index]:  # 越界锁定
                endp = self.RIGHTB[index]
            # 如果该点灰度大于对比点,且该点与对比点的对比度大于阈值
            if ccd_data[i] > (ccd_data[endp] + 8):
                if ((ccd_data[i] - ccd_data[endp]) * 100 // (ccd_data[i] + ccd_data[endp])) > self.WBVALUE[index]:
                    self.last_rightp[index] = self.rightp[index]
                    self.rightp[index] = endp  # 该对比点为右边界
                    self.flag_r[index] = 1  # 则找到右边线
                    break  # 退出遍历
        # 丢线处理
        if index == 0:
            pass
            if self.flag_l[0] == 0 and self.flag_r[0] == 0:
                # self.search[0] = 40
                self.rightp[0] = 85
                self.leftp[0] = 39

        elif index == 1:
            if self.flag_l[index] == 0 and self.flag_r[index] == 0:  # 如果左右同时丢线
                # self.search[index] = 45  # 增大近端搜索半径
                # 丢线处理
#                 if self.ramp_flag:
#                     self.Loss_Count = 0
                if self.roadwidth[index] < 10 or self.ccd_aver[index] < 23 and self.ramp_flag == 0:  # 如果检测到路长小于10个点,并且CCD的平均值小于40
                    self.loss_flag = 1  # 判断为 丢线
                    self.Loss_Count = self.Loss_Count + 1  # 丢线计时位累加
                    if self.Loss_Count > 200:  # 当丢线计时大于20
                        self.stop_flag = 2  # 停车标志位置1
                        beep.on()
            else:
                self.Loss_Count = 0  # 丢线计时清0
            # 坐标和搜索半径等异常处理
            if (self.flag_l[1] == 0 and self.flag_r[1] == 0) and self.annu_flag <= 1:
                self.leftp[1] = 28
                self.rightp[1] = 92
                self.loss_flag = 1

        if self.flag_l[index] == 1 and self.flag_r[index] == 1:
            if self.leftp[index] > self.rightp[index]:
                self.rightp[index] = 85
                self.leftp[index] = 39
                self.flag_r[index] = 0
                self.flag_l[index] = 0
            self.search[index] = 15  # 两边不丢线回复搜索范围
            if self.loss_flag == 1:  # 左右搜到线的情况下
                self.loss_flag = 0  # 将丢线标志位清零
        # 未全丢线计算坐标
        if self.flag_l[index] or self.flag_r[index]:
            self.CCD_location[index] = ((self.rightp[index] + self.leftp[index]) // 2 - self.CENTER[index])  # 拐点中值-中间点
            self.roadwidth[index] = (self.rightp[index] - self.leftp[index])  # 计算路宽
            self.CCD_location[index] = limit_min_max(self.CCD_location[index], -40, 40)  # location限幅
            if abs(self.CCD_location[index]) < 2 and index == 0:
                self.CCD_location[index] = 0

    def Element_process(self):
        self.postion_fitting()  # 位置拟合
#         if self.ramp_flag == 0 and time_count - self.ramp_cd >= 100:
        self.start_line(ccd_data2)  # 起跑线
        if self.annu_flag <= 1:
            self.brick()  # 障碍
#         if self.ramp_flag == 0 and self.annu_flag <= 1:
#             self.brick()  # 障碍
#         if time_count - self.ramp_cd >= 300:
#             self.ramp()  # 坡道
#         if 1 <= self.ramp_flag <= 3:
#             self.Fitting_location = limit_min_max(self.Fitting_location, -10, 10)
        if self.annu_num < self.annu_set_num  and time_count - self.annu_cd >= 100:
            self.Cirque()
            self.Cirque_deal()

    def postion_fitting(self):
        if self.CCD_location[1] * self.CCD_location[0] > 0 or abs(self.CCD_location[1] - self.CCD_location[0]) < 17:
            self.fit_loction1 = self.CCD_location[0]
        self.Fitting_location = int((self.CCD_location[1] * 7 / 10) + (self.fit_loction1 * 3 / 10))
        self.Fitting_speed_location = int((self.CCD_location[1] * 3 / 10) + (self.fit_loction1 * 7 / 10))
        # 十字处理
        if self.ccd_aver[1] >= 70 and self.flag_l[1] == 0 and self.flag_r[1] == 0 and self.annu_flag <= 1:
            if self.flag_l[0] or self.flag_r[0]:
                self.Fitting_location = self.CCD_location[0]
                self.annu_cd = time_count
            self.CCD_location[1] = 0
            
    def start_line(self, ccd_data):
        self.point_count = 0
        for i in range(30, 95):
            endp = i + 1  # 对比点(该点左边第'1'个点)
            if (ccd_data[i] - ccd_data[endp]) * 100 // (ccd_data[i] + ccd_data[endp]) > 20:
                self.point_count += 1
        if self.point_count > 5:
            self.startline_flag = 1
        if self.startline_flag == 1:
            self.encoder_intergral += motor_user.aver_speed
        else:
            self.encoder_intergral = 0
        if self.encoder_intergral >= min(600, 600 + (120 - motor_user.aver_speed) * 10):
            self.stop_flag = 3
            
            
    def brick(self):
        if self.brick_flag <= 1:
            if self.flag_r[0] == 1 and self.flag_l[0] == 1 and 25 <= self.roadwidth[0] <= 33:
                self.brick_flag = 1
                self.brick_cnt += 1
                if self.leftp[1] - self.last_leftp[1] >= 8 and self.brick_cnt >= 8:
                    self.brick_direction = self.B_LEFT
                    self.brick_flag = 2
                elif self.last_rightp[1] - self.rightp[1] >= 8 and self.brick_cnt >= 10:
                    self.brick_direction = self.B_RIGHT
                    self.brick_flag = 2
            else:
                self.brick_cnt = 0
                self.brick_flag = 0

        if self.brick_flag == 2:
            beep.on()
            self.brick_cnt += motor_user.aver_speed
            if self.brick_direction == self.B_RIGHT:
                t_right = self.leftp[1] + 25   #25
                self.Fitting_location = (t_right + self.leftp[1]) // 2 - self.CENTER[1]
                self.Fitting_location = limit_min_max(self.Fitting_location, -40, 40)
            elif self.brick_direction == self.B_LEFT:
                t_left = self.rightp[1] - 25
                self.Fitting_location = (t_left + self.rightp[1]) // 2 - self.CENTER[1]
                self.Fitting_location = limit_min_max(self.Fitting_location, -40, 40)
            else:
                self.brick_flag = 0
                self.brick_cnt = 0
                beep.off()
            if self.brick_cnt > 3200:
                self.brick_flag = 0
                self.brick_cnt = 0
                self.brick_direction = 0
                beep.off()
#     def ramp(self):
#         if self.ramp_flag == 0:
#             if self.flag_r[0] and self.flag_l[0] and self.flag_r[1] and self.flag_l[1]:
#                 if self.roadwidth[0] - self.ROADWIDTH[0] > 18 and self.roadwidth[1] - self.ROADWIDTH[1] > 13:
#                     self.ramp_flag = 1
#                     self.ramp_up_angle = 0
#                     beep.on()
#         elif self.ramp_flag == 1:
#             if self.roadwidth[0] - self.ROADWIDTH[0] > 25 and self.roadwidth[1] - self.ROADWIDTH[1] > 20:
#                 self.ramp_move = motor_user.carmove
#                 self.ramp_up_flag = 1
#                 # self.flag_r = [0, 0]
#                 # self.flag_l = [0, 0]
#                 self.ramp_flag = 2
#         elif self.ramp_flag == 2:
#             if motor_user.carmove - self.ramp_move > 10000:
#                 self.leftp = [45, 33]
#                 self.rightp = [83, 90]
#                 self.ramp_flag = 3
#                 self.ramp_up_flag = 0
#                 self.ramp_up_angle = 0
#                 beep.off()
#         elif self.ramp_flag == 3:
#             beep.on()
#             if (self.flag_l[0] and self.flag_r[0]) or (self.flag_l[1] and self.flag_r[1]):
#                 if abs(self.Fitting_location) <= 6:
#                     self.ramp1_cnt += 1
#             if self.ramp1_cnt > 15:
#                 self.ramp_flag = 4
#         elif self.ramp_flag == 4:
#             beep.off()
#             if self.flag_r[0] and self.flag_l[0] and self.flag_r[1] and self.flag_l[1]:
#                 self.ramp_cd = time_count
#                 self.ramp_flag = 0
# 
#         if self.ramp_up_flag == 1:
#             self.ramp_up_angle += int(motor_user.imu660_gyro_z // 10)
#             self.ramp_up_angle = limit_min_max(self.ramp_up_angle, -5000, 5000)
            
    def Cirque(self):#28 60 
        if self.annu_flag == 0:
            if self.flag_l[1] == 1 and self.flag_r[1] == 0 and self.flag_r[0] == 1 and self.flag_l[1] == 1 and 28 < \
                    self.roadwidth[0] < 60:
                self.annu_dir = self.RIGHT
                self.annu_flag = 1
                self.now_leftp = self.leftp[0] - self.leftp[1]
                self.now_rightp = self.rightp[1] - self.rightp[0]

            elif self.flag_l[1] == 0 and self.flag_r[1] == 1 and self.flag_r[0] == 1 and self.flag_l[0] == 1 and 28 < \
                    self.roadwidth[0] < 60:
                self.annu_dir = self.LEFT
                self.annu_flag = 1
                self.now_leftp = abs(self.leftp[0] - self.leftp[1])
                self.now_rightp = abs(self.rightp[1] - self.rightp[0])

        if self.annu_flag == 1:
            if self.flag_l[1] == 1 and self.flag_r[1] == 0 and abs(
                    abs(self.leftp[0] - self.leftp[1]) - self.now_leftp) <= 7 \
                    and self.annu_dir == self.RIGHT and 28 < self.roadwidth[0] < 60:
                    #and 40 <= self.roadwidth[1] <= 60 and self.annu_dir == self.RIGHT:
                self.keepmove1 += motor_user.aver_speed
            elif self.flag_l[1] == 0 and self.flag_r[1] == 1 and abs(
                    abs(self.rightp[1] - self.rightp[0]) - self.now_rightp) <= 7 \
                    and self.annu_dir == self.LEFT and 28 < self.roadwidth[0] < 60:
                    #and 40 <= self.roadwidth[1] <= 60 and self.annu_dir == self.LEFT:
                self.keepmove1 += motor_user.aver_speed
            else:
                self.keepmove1 = 0
                self.annu_flag = 0
            if self.keepmove1 > self.annu1_move:
                self.annu_flag = 2

        if self.annu_flag == 2:
            self.annu_angle_flag = 1  # 陀螺仪开始积分
            if self.rightp[1] < self.pre_rightp and self.flag_r[1] == 1 and self.flag_l[1] == 1:
                self.pre_rightp = self.rightp[1]
            if self.leftp[1] > self.pre_leftp and self.flag_r[1] == 1 and self.flag_l[1] == 1:
                self.pre_leftp = self.leftp[1]

            if self.annu_dir == self.RIGHT and self.flag_r[1] == 1 and self.rightp[1] > self.pre_rightp + 7:
                self.annu_flag = 3
            elif self.annu_dir == self.LEFT and self.flag_l[1] == 1 and self.leftp[1] < self.pre_leftp - 7:
                self.annu_flag = 3

        if self.annu_flag == 3 and self.flag3_loss == 1:
            if (self.flag_l[1] == 1 and self.annu_dir == self.RIGHT and self.leftp[1] >= 26) or (self.flag_r[1] == 1
                                                                                                 and self.annu_dir == self.LEFT and
                                                                                                 self.rightp[1] <= 81):
                self.flag3_loss = 0
                self.annu_flag = 4
        if self.annu_flag == 4 and self.flag4_loss == 1:
            # if (self.flag_l[1] == 1 and self.annu_dir == self.RIGHT and self.annu_angle > 18000) or (
            #         self.flag_r[1] == 1 and self.annu_dir == self.LEFT and self.annu_angle > 18000):  # 最初15000
            if (self.annu_dir == self.RIGHT and self.annu_angle > 38000) or (
                    self.annu_dir == self.LEFT and self.annu_angle > 38000):  # 最初15000
                self.annu_flag = 5
                self.flag4_loss = 0
                self.keepmove4 = motor_user.carmove
        if self.annu_flag == 5:
            if (motor_user.carmove - self.keepmove4) > 8000:  # and (=self.flag_r[1] == 1 and self.flag_l[1] == 1):
                self.annu_flag = 6
                self.keepmove6 = motor_user.carmove
        if self.annu_flag == 6:
            beep.on()
            if motor_user.carmove - self.keepmove6 > 9000:  #12500
                beep.off()
                self.annu_flag = 0
                self.keepmove1 = 0
                self.keepmove4 = 0
                self.keepmove6 = 0
                self.now_rightp = 0
                self.now_leftp = 0
                self.annu_angle = 0
                self.annu_dir = 0
                self.flag5_loss = 0
                self.pre_rightp = 128
                self.pre_leftp = 0
                self.annu_angle_flag = 0
                self.annu_num += 1

    def Cirque_deal(self):
        if self.annu_flag == 2:
            beep.on()
            if self.annu_dir == self.RIGHT:
                t_right = self.leftp[1] + self.ROADWIDTH[1]
                self.Fitting_location = (t_right + self.leftp[1]) // 2 - self.CENTER[1]
            if self.annu_dir == self.LEFT:
                t_left = self.rightp[1] - self.ROADWIDTH[1]
                self.Fitting_location = (t_left + self.rightp[1]) // 2 - self.CENTER[1]
        # 圆环状态3
        if self.annu_flag == 3:
            beep.off()
            if self.annu_dir == self.RIGHT:
                if self.flag_r[1] == 0 and self.rightp[1] < self.annu3_right_point:
                    self.rightp[1] = self.rightp[1] + 1
                self.rightp[1] = min(self.rightp[1], self.annu3_right_point)
                t_left = self.rightp[1] - self.annu3_lenth
                self.Fitting_location = (t_left + self.rightp[1]) // 2 - self.CENTER[1]
                self.Fitting_location = limit_min_max(self.Fitting_location, 10, 30)

            elif self.annu_dir == self.LEFT:
                if self.flag_l[1] == 0 and self.leftp[1] > self.annu3_left_point:
                    self.leftp[1] = self.leftp[1] - 1
                self.leftp[1] = max(self.leftp[1], self.annu3_left_point)
                t_right = self.leftp[1] + self.annu3_lenth
                self.Fitting_location = (t_right + self.leftp[1]) // 2 - self.CENTER[1]
                self.Fitting_location = limit_min_max(self.Fitting_location, -30, -10)

        if self.annu_flag == 4:
            beep.on()
            self.Fitting_location = self.CCD_location[1]
            if self.annu_dir == self.RIGHT:
                self.rightp[1] = max(self.rightp[1], 52)
            if self.annu_dir == self.LEFT:
                self.leftp[1] = min(self.leftp[1], 74)

        if self.annu_flag == 5:
            beep.off()
            if self.annu_dir == self.RIGHT and self.flag_r[1] == 0:
                t_right = self.leftp[1] + self.annu5_lenth
                self.Fitting_location = (t_right + self.leftp[1]) // 2 - self.CENTER[1]
                # self.Fitting_location = limit_min_max(self.Fitting_location, 15, 22)

            if self.annu_dir == self.LEFT and self.flag_l[1] == 0:
                t_left = self.rightp[1] - self.annu5_lenth
                self.Fitting_location = (t_left + self.rightp[1]) // 2 - self.CENTER[1]
                # self.Fitting_location = limit_min_max(self.Fitting_location, -22, -15)

        if self.annu_flag == 3:
            if self.flag_l[1] == 0 and self.annu_dir == self.RIGHT:
                # self.Fitting_location = limit_min_max(self.Fitting_location, 20, 40)
                self.flag3_loss = 1
                self.leftp[1] = self.rightp[1] - self.ROADWIDTH[1]
                self.leftp[1] = limit_min_max(self.leftp[1], self.LEFTB[1], self.RIGHTB[1])
                self.rightp[1] = limit_min_max(self.rightp[1], self.LEFTB[1], self.RIGHTB[1])
                self.search[1] = 40

            elif self.flag_r[1] == 0 and self.annu_dir == self.LEFT:
                # self.Fitting_location = limit_min_max(self.Fitting_location, -40, -20)
                self.flag3_loss = 1
                self.rightp[1] = self.leftp[1] + self.ROADWIDTH[1]
                self.leftp[1] = limit_min_max(self.leftp[1], self.LEFTB[1], self.RIGHTB[1])
                self.rightp[1] = limit_min_max(self.rightp[1], self.LEFTB[1], self.RIGHTB[1])
                self.search[1] = 40
        elif self.annu_flag == 5:
            if self.flag_l[1] == 1 and self.flag_r[1] == 0 and self.annu_dir == self.RIGHT:
                self.flag5_loss = 1
                self.rightp[1] = 80
                self.search[1] = 30
            if self.flag_r[1] == 1 and self.flag_l[1] == 0 and self.annu_dir == self.LEFT:
                self.flag5_loss = 1
                self.leftp[1] = 50
                self.search[1] = 30

        if self.annu_angle_flag == 1:
            self.annu_angle += int(abs(motor_user.imu660_gyro_z) // 25)
            if self.annu_angle > 9000:  # 最初13000
                if self.flag_l[1] == 0 and self.annu_dir == self.RIGHT and self.annu_flag == 4:
                    # if self.annu_dir == self.RIGHT:
                    self.flag4_loss = 1
                    self.rightp[1] = max(self.rightp[1], 45)
                    t_left = self.rightp[1] - self.annu3_lenth
                    self.Fitting_location = (t_left + self.rightp[1]) // 2 - self.CENTER[1]
                    self.Fitting_location = limit_min_max(self.Fitting_location, 18, 23)

                elif self.flag_r[1] == 0 and self.annu_dir == self.LEFT and self.annu_flag == 4:
                    # elif self.annu_dir == self.LEFT:
                    self.flag4_loss = 1
                    self.leftp[1] = min(self.leftp[1], 81)
                    t_right = self.leftp[1] + self.annu3_lenth
                    self.Fitting_location = (t_right + self.leftp[1]) // 2 - self.CENTER[1]
                    self.Fitting_location = limit_min_max(self.Fitting_location, -23, -18)

class Motor_User:
    """整合所有控制器的电机控制类"""
    
    def __init__(self):
        # 编码器数据
        self.encl_data = 0
        self.encr_data = 0
        self.aver_speed = 0
        self.carmove = 0
        
        # 控制状态
        self.stop_cnt_flag = 0
        self.stop_cnt = 0
        self.protect_cnt = 0
        self.speed_Start = 0 #不加转向环输出
        self.reslut = 0
        self.out = 0
        # 陀螺仪数据
        self.imu660_gyro_z = 0
        
        # 转向外环PD参数 - 优化过弯稳定性
        self.camera_kp = 60   # 降低比例系数，减少过度反应
        self.camera_kd = 35   # 增加微分系数，提高稳定性
        self.camera_kp2 = 8   # 降低非线性项，避免急转
        self.camera_kd2 = 0
        self._err_last = 0
        #转向内环pd参数 - 增强阻尼
        self.kp = 1.5         # 略微降低比例系数
        self.kd = 0.15        # 增加微分系数，提高稳定性
        self.err_last = 0

        # 弯道检测和速度控制参数
        self.curve_threshold_light = 8   # 轻微弯道阈值
        self.curve_threshold_medium = 15 # 中等弯道阈值
        self.curve_threshold_sharp = 25  # 急弯阈值
        self.base_speed = 90             # 基础速度
        self.curve_speed_factor = 0.7    # 弯道速度系数
        # 直立环PD参数
        self.vertical_kp = 400
        self.vertical_kd = -300
        self.vertical_err_last = 0
        
        # 角速度环PI参数
        self.angular_kp = 1900   #2000 600
        self.angular_ki = 400
        self.angular_integral = 0
        self.angular_err_last = 0
        
        # 速度环PID参数
        self.speed_kp = 6000
        self.speed_ki = 0
        self.speed_kd = 3000
        self.speed_integral = 0
        self.speed_filtered_err = 0
        self.speed_err_last = 0
        # 互补滤波器参数
        self.filter_gyro_ratio = 1250
        self.filter_acc_ratio = 1000
        self.filter_call_cycle = 5
        self.filter_mechanical_zero =  1700 #2030
        self.filter_angle = 0
        
    def motor_control(self):
        # 理想速度计算
        #self.right_ispeed = self.left_ispeed = self.ideal_speed_control()
        # 特殊速度处理
        #self.special_speed()
        # 差速计算
        self.camera_pd_update()
        self.camera_pds_update()
        if ccd_user.stop_flag:  # 如果停车标志位为1
            self.left_ispeed = 0
            self.right_ispeed = 0
            if self.stop_cnt_flag == 0:
                self.stop_cnt = time_count
                self.stop_cnt_flag = 1
           
          # ------------ 速度环 (100次周期) ------------
        
        set_L = self.speed_Start - self.out  #- +
        set_R = self.speed_Start + self.out
#         set_L = self.speed_Start  
#         set_R = self.speed_Start 
        if self.stop_cnt_flag:
            if time_count - self.stop_cnt >= 50:
                set_L = 0
                set_R = 0
        if ccd_user.stop_flag:
            if self.encl_data < 20 and self.encr_data < 20:
                set_L = 0
                set_R = 0
        if abs(self.encl_data) > 350 or abs(self.encr_data) > 350:
            self.protect_cnt += 1
            if self.protect_cnt >= 10:
                set_L = 0
                set_R = 0
        else:
            self.protect_cnt = 0
        motor_3.duty(set_R)  # 右电机pwm幅值
        motor_4.duty(set_L)  # 左电机pwm幅值
        
    def camera_pd_update(self):
        """转向外环PD控制器更新 - 优化过弯稳定性"""

        # 整数运算
        err = ccd_user.Fitting_location
        d_err = err - self._err_last

        # 根据弯道程度动态调整参数
        abs_err = abs(err)
        if abs_err > self.curve_threshold_sharp:
            # 急弯：降低响应，增加稳定性
            kp_dynamic = self.camera_kp * 0.6
            kd_dynamic = self.camera_kd * 1.5
            kp2_dynamic = self.camera_kp2 * 0.3
        elif abs_err > self.curve_threshold_medium:
            # 中等弯道：适中响应
            kp_dynamic = self.camera_kp * 0.8
            kd_dynamic = self.camera_kd * 1.2
            kp2_dynamic = self.camera_kp2 * 0.6
        else:
            # 直道或轻微弯道：正常响应
            kp_dynamic = self.camera_kp
            kd_dynamic = self.camera_kd
            kp2_dynamic = self.camera_kp2

        out = (kp_dynamic * err + kd_dynamic * d_err + kp2_dynamic * abs(err) * err + self.camera_kd2 * (self.imu660_gyro_z))

        self._err_last = err

        # 根据弯道程度动态调整输出限幅
        if abs_err > self.curve_threshold_sharp:
            limit_val = 3000  # 急弯时限制转向幅度
        elif abs_err > self.curve_threshold_medium:
            limit_val = 4500  # 中等弯道
        else:
            limit_val = 6000  # 直道

        self.result = limit_min_max(out, -limit_val, limit_val)
        
    
    def camera_pds_update(self):
        """转向内环PD控制器更新 - 增强稳定性"""

        error = self.result + self.imu660_gyro_z
        d_err = error - self.err_last

        # 根据误差大小动态调整内环参数
        abs_error = abs(error)
        if abs_error > 3000:  # 大误差时增加阻尼
            kp_inner = self.kp * 0.8
            kd_inner = self.kd * 2.0
        elif abs_error > 1500:  # 中等误差
            kp_inner = self.kp * 0.9
            kd_inner = self.kd * 1.5
        else:  # 小误差时正常响应
            kp_inner = self.kp
            kd_inner = self.kd

        output = (kp_inner * error + kd_inner * d_err)

        # 根据误差动态调整输出限幅
        if abs_error > 3000:
            limit_val = 3500  # 大误差时限制输出
        elif abs_error > 1500:
            limit_val = 5000  # 中等误差
        else:
            limit_val = 6000  # 小误差时正常限幅

        self.out = limit_min_max(output, -limit_val, limit_val)
        self.err_last = error

    def get_dynamic_speed(self):
        """根据弯道程度动态调整目标速度"""
        abs_err = abs(ccd_user.Fitting_location)

        if abs_err > self.curve_threshold_sharp:
            # 急弯：大幅降速
            target_speed = int(self.base_speed * 0.5)
        elif abs_err > self.curve_threshold_medium:
            # 中等弯道：适度降速
            target_speed = int(self.base_speed * 0.7)
        elif abs_err > self.curve_threshold_light:
            # 轻微弯道：略微降速
            target_speed = int(self.base_speed * 0.85)
        else:
            # 直道：可以加速
            target_speed = int(self.base_speed * 1.1)

        # 限制速度范围
        target_speed = limit_min_max(target_speed, 40, 120)
        return target_speed

    def enhance_curve_stability(self):
        """增强过弯稳定性的综合控制"""
        abs_err = abs(ccd_user.Fitting_location)

        # 检测连续弯道
        if hasattr(self, 'curve_history'):
            self.curve_history.append(abs_err)
            if len(self.curve_history) > 10:
                self.curve_history.pop(0)
        else:
            self.curve_history = [abs_err] * 10

        # 计算弯道连续性
        avg_curve = sum(self.curve_history) / len(self.curve_history)

        # 连续弯道检测
        if avg_curve > self.curve_threshold_medium:
            # 连续弯道模式：更保守的控制
            self.is_continuous_curve = True
            # 进一步降低转向增益
            self.curve_stability_factor = 0.7
        else:
            self.is_continuous_curve = False
            self.curve_stability_factor = 1.0

        return self.curve_stability_factor

    def vertical_pd_update(self, speed_ff, actual_angle):
        """直立环PD控制器更新"""
        error = actual_angle - speed_ff
        derivative = error - self.vertical_err_last
        
        # 整数运算
        output = (self.vertical_kp * error + 
                  self.vertical_kd * derivative) // 1000
        
        # 限幅处理
        output = limit_min_max(output,-4000,4000)
        
        self.vertical_err_last = error
        return output

    def angular_pi_update(self, target, actual):
        """角速度环PI控制器更新"""
        error = target - actual
        self.angular_integral += error * self.angular_ki // 1000
        
        self.angular_integral = limit_min_max(self.angular_integral,-2000,2000)
        
        output = (self.angular_kp * error) // 1000 + self.angular_integral
        
        # 积分抗饱和
        output = limit_min_max(output,-4000,4000)
        
        self.angular_err_last = error
        return output

    def speed_pid_update(self, target, actual):
        """速度环PID控制器更新"""
        # 1. 计算原始误差
        raw_error = actual - target
        
        # 2. 低通滤波优化
        self.speed_filtered_err = (4 * self.speed_filtered_err + raw_error) // 5
        
        # 3. 计算微分项
        derivative = self.speed_filtered_err - self.speed_err_last
        self.speed_err_last = self.speed_filtered_err
        
        # 4. 积分项处理 (单独限幅)
        integral_temp = self.speed_integral + self.speed_filtered_err * self.speed_ki // 1000
        integral_temp = limit_min_max(integral_temp,-2000,2000)
        
        # 5. 计算输出并限幅
        output = (self.speed_kp * self.speed_filtered_err + 
                  self.speed_kd * derivative) // 1000 + self.speed_integral
        
        # 输出限幅
        output = limit_min_max(output,-4000,4000)
        return output

    def complementary_filter_update(self, gyro_raw, acc_raw):
        """互补滤波器更新"""
        # 传感器原始值转换
        gyro = (gyro_raw * self.filter_gyro_ratio) // 1000
        acc = (acc_raw * self.filter_acc_ratio) // 1000
        
        # 互补滤波核心
        acc_deviation = acc - self.filter_angle
        delta = (gyro + acc_deviation) * self.filter_call_cycle
        self.filter_angle += delta // 1000
        
        # 机械零点补偿
        return self.filter_angle + self.filter_mechanical_zero
#     def ips200_display():
#     ccd_data1 = ccd.get(0)
#     ccd_data2 = ccd.get(1)
#     
#     lcd.str16(0,  0, "Pitch={:f}.".format(round(Imu.Pitch,3)),0x001F)
#     lcd.str16(0,  16, "Yaw={:f}.".format(Imu.Yaw), 0x001F)
#     lcd.str16(0, 150, "med_speed{:>6d}, turn{:>6f}.".format(med_speed,turn), 0x001F)
#     lcd.str16(0, 168, f"{road:.2f}".format(road), 0x001F)
#     lcd.str16(0, 285,f"motor1: {motor1:.2f},motor2:{motor2:.2f}", 0x001F)
#     lcd.str16(0, 223, "g = {:>6f}, {:>6f}, {:>6f}.".format(Imu.gyro_x,Imu.gyro_y, Imu.gyro_z), 0x001F)
#     
motor_3 = MOTOR_CONTROLLER(MOTOR_CONTROLLER.PWM_C24_DIR_C26, 13000, duty = 0, invert = True)
motor_4 = MOTOR_CONTROLLER(MOTOR_CONTROLLER.PWM_C25_DIR_C27, 13000, duty =0, invert = True)

ccd_user = CCD_User()
motor_user = Motor_User()
ticker_flag = False
ticker_count = 0
time_count = 0
imu_data = imu.get()
duty_Speed = 0
duty_up = 0
c = 0
bn = 0
def time_pit0_handler(time):
    global ticker_flag, ticker_count, time_count,duty_Speed,duty_up,bn,c
    c=imu_data[3]-6.5
    d=imu_data[5]
    if c > -5 and c < 5:
        c = 0
    if d > -5 and d < 5:
        d = 0
    motor_user.imu660_gyro_z = d
    ticker_count = ticker_count + 1
    ticker_flag = True
    
    motor_user.encl_data = encoder_1.get()  # 左编码器采值
    motor_user.encr_data = encoder_2.get()  # 右编码器采值
    motor_user.aver_speed = (motor_user.encr_data + motor_user.encl_data) // 2
    time_count = time_count + 1
    motor_user.carmove += int(motor_user.aver_speed)
    gyro_data = c    # 示例陀螺仪原始值（角速度正方向）
    acc_data = imu_data[1] 
    bn = motor_user.complementary_filter_update(gyro_data, acc_data) 
    # ------------ 速度环 (100次周期) ------------
    if(ticker_count % 10 == 0):
        # 使用动态速度控制
        target_speed = motor_user.get_dynamic_speed()
        duty_Speed = motor_user.speed_pid_update(target_speed, motor_user.aver_speed)
        duty_Speed = max(min(duty_Speed, 500), -500)
    # ------------ 直立环 (10次周期) ------------
    if(ticker_count % 4 == 0):
        duty_up = motor_user.vertical_pd_update(duty_Speed,int(bn)) #-1030
    # ------------ 角速度环 (每次执行) ------------
    speed_Start = motor_user.angular_pi_update(-duty_up, int(gyro_data))  #-duty_up  -4
    motor_user.speed_Start = speed_Start
    motor_user.motor_control()
    gc.collect()
# 实例化 PIT ticker 模块 参数为编号 [0-3] 最多四个
pit0 = ticker(0)
pit0.capture_list(imu,encoder_1,encoder_2)
pit0.callback(time_pit0_handler)
pit0.start(5)

pit1 = ticker(1)
pit1.capture_list(ccd)
pit1.start(5)

switch2 = Pin('D9' , Pin.IN , pull = Pin.PULL_UP_47K, value = True)
state2  = switch2.value()

while True:
    ccd_data1 = [min(max(x, ccd_user.gray_min), ccd_user.gray_max) for x in ccd.get(0)]  # 获取CCD1通道数据
    ccd_data2 = [min(max(x, ccd_user.gray_min), ccd_user.gray_max) for x in ccd.get(1)]  # 获取CCD1通道数据
    ccd_user.ccd_handle(ccd_data1, 0)
    ccd_user.ccd_handle(ccd_data2, 1)
    ccd_user.ccd_search(ccd_data1, 0)  # ccd1搜线
    ccd_user.ccd_search(ccd_data2, 1)  # ccd2搜线
    ccd_user.Element_process()# 元素处理
    
#     lcd.line(0, 120, ccd_user.leftp[0], 120, 0x0000)  # 左边际
#     lcd.line(0, 120, ccd_user.rightp[0], 120, 0x0000)  # 右边际
#     lcd.line(0, 120, ccd_user.CENTER[0] + ccd_user.CCD_location[0], 120, 0xFF00)  # 中值
# 
#     # CCD2
#     lcd.line(0, 140, ccd_user.leftp[1], 140, 0x0000)  # 左边际
#     lcd.line(0, 140, ccd_user.rightp[1], 140, 0x0000)  # 右边际
#     lcd.line(0, 140, ccd_user.CENTER[1] + ccd_user.CCD_location[1], 140, 0xFF00)  # 中值

    # 显示文字信息
    lcd.str16(0, 0, "CCD1 Left: {:<3}".format(ccd_user.leftp[0]))
    lcd.str16(0, 16, "CCD1 Right: {:<3}".format(ccd_user.rightp[0]))
    lcd.str16(0, 32, "CCD1 Center: {:<3}".format(ccd_user.CENTER[0] + ccd_user.CCD_location[0]))
    lcd.str16(0, 48, "CCD2 Left: {:<3}".format(ccd_user.leftp[1]))
    lcd.str16(0, 64, "CCD2 Right: {:<3}".format(ccd_user.rightp[1]))
    lcd.str16(0, 80, "CCD2 Center: {:<3}".format(ccd_user.CENTER[1] + ccd_user.CCD_location[1]))
#     lcd.str16(0, 16 * 9, "integral:{:<4}".format(zxc.integral))  # 显示字符串
# #     lcd.str16(120, 16 * 9, "CCD2_lct:{:<4}".format(ccd_user.CCD_location[1]))  # 显示字符串
#     lcd.str16(0, 16 * 10, "sudu:{:<4}".format(duty_Speed))
#     lcd.str16(0, 16 * 11, "out:{:<4}".format(speed_Start))# 显示字符串
    # 如果拨码开关打开 对应引脚拉低 就退出循环
    # 这么做是为了防止写错代码导致异常 有一个退出的手段
    if switch2.value() != state2:     
        pit0.stop()
        pit1.stop()
        print("Test program stop.")
        break
    
    # 回收内存
    gc.collect()











































